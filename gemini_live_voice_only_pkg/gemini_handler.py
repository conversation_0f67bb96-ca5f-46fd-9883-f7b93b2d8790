import asyncio
import base64
import numpy as np
from typing import Async<PERSON><PERSON>ator, Literal
from google import genai
from google.genai import types

from google.genai.types import (
    LiveConnectConfig,
    PrebuiltVoiceConfig,
    SpeechConfig,
    VoiceConfig,
    Content,
    Part,
    Tool,
    FunctionDeclaration,
    Schema,
    GoogleSearch,
    ToolCodeExecution,

)
from fastrtc import AsyncStreamHandler, wait_for_item
from gemini_live_voice_only_pkg.tools import search_products
from gemini_live_voice_only_pkg.prompts import system_prompt

def encode_audio(data: np.ndarray) -> str:
    """Encode audio data to send to the server."""
    return base64.b64encode(data.tobytes()).decode("UTF-8")

class GeminiHandler(AsyncStreamHandler):
    """Hand<PERSON> for interacting with the Gemini API."""
    def __init__(
        self,
        api_key: str,
        system_prompt: str,
        voice_name: str,
        expected_layout: Literal["mono"] = "mono",
        output_sample_rate: int = 24000,
        output_frame_size: int = 480,
        input_sample_rate: int = 16000,
    ) -> None:
        super().__init__(expected_layout, output_sample_rate, output_frame_size, input_sample_rate=input_sample_rate)
        self.api_key = api_key
        self.system_prompt = system_prompt
        self.voice_name = voice_name
        self.input_queue: asyncio.Queue = asyncio.Queue()
        self.output_queue: asyncio.Queue = asyncio.Queue()
        self.quit: asyncio.Event = asyncio.Event()

    def copy(self) -> "GeminiHandler":
        return GeminiHandler(
            api_key=self.api_key,
            system_prompt=self.system_prompt,
            voice_name=self.voice_name,
            expected_layout="mono",
            output_sample_rate=self.output_sample_rate,
            output_frame_size=self.output_frame_size,
        )

    async def start_up(self):
        client = genai.Client(
            api_key=self.api_key,
            http_options={"api_version": "v1beta"},
        )
        config = LiveConnectConfig(
            response_modalities=["AUDIO"],
            system_instruction=Content(
                parts=[Part(text=system_prompt)]
            ),
            media_resolution="MEDIA_RESOLUTION_MEDIUM",
            speech_config=SpeechConfig(
                voice_config=VoiceConfig(
                    prebuilt_voice_config=PrebuiltVoiceConfig(
                        voice_name=self.voice_name,
                    )
                )
            ),
            context_window_compression=types.ContextWindowCompressionConfig(
                trigger_tokens=25600,
                sliding_window=types.SlidingWindow(target_tokens=12800),
            ),
            tools=[
                search_products
            ],
            output_audio_transcription={},
            realtime_input_config=types.RealtimeInputConfig(
                automatic_activity_detection=types.AutomaticActivityDetection(
                    start_of_speech_sensitivity=types.StartSensitivity.START_SENSITIVITY_LOW,
                    end_of_speech_sensitivity=types.EndSensitivity.END_SENSITIVITY_LOW,
                    prefix_padding_ms=200,
                    silence_duration_ms=500
                ),
                activity_handling=types.ActivityHandling.NO_INTERRUPTION
            )
        )
        async with client.aio.live.connect(
            model="models/gemini-2.5-flash-preview-native-audio-dialog", config=config
        ) as session:
            async for audio in session.start_stream(
                stream=self.stream(), mime_type="audio/pcm"
            ):
                if audio.data:
                    array = np.frombuffer(audio.data, dtype=np.int16)
                    self.output_queue.put_nowait((self.output_sample_rate, array))
            # await session.send(input="hi omiii!", end_of_turn=True)

            async for response in session.receive():
                if response.tool_call:
                    # for function_call in response.tool_call.function_calls:
                    #     print(f"**FunctionCall >** {str(function_call)}")
                    # await session.send(
                    #     input="temperature is hot at 49C!", end_of_turn=True
                    # )
                    function_responses = []
                    for fc in response.tool_call.function_calls:
                        # print(fc.name, fc)

                        if fc.name == "search_products":
                            function_response = types.FunctionResponse(
                                id=fc.id,
                                name=fc.name,
                                response=search_products(**fc.args)
                                )
                            function_responses.append(function_response)

                    await session.send_tool_response(function_responses=function_responses)

    async def stream(self) -> AsyncGenerator[bytes, None]:
        while not self.quit.is_set():
            try:
                audio = await asyncio.wait_for(self.input_queue.get(), 0.1)
                yield audio
            except (asyncio.TimeoutError, TimeoutError):
                continue

    async def receive(self, frame: tuple[int, np.ndarray]) -> None:
        _, array = frame
        array = array.squeeze()
        audio_message = encode_audio(array)
        self.input_queue.put_nowait(audio_message)

    async def emit(self) -> tuple[int, np.ndarray] | None:
        return await wait_for_item(self.output_queue)

    def shutdown(self) -> None:
        self.quit.set()
