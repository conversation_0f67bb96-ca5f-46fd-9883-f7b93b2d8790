from typing import Optional

def search_products(query:Optional[str] = None) -> str:
    """Searches for products with matching query, if query is none it returns all the products"""
    return {"products":[
  {
    "name": "Artisan Coffee Blend",
    "choices": {
      "quantity_options": ["250g", "500g", "1kg"],
      "flavor_options": ["Dark Roast", "Medium Roast", "Light Roast"],
      "grind_options": ["Whole Bean", "Espresso", "Drip"]
    },
    "description": "A rich and complex coffee blend with notes of chocolate and fruit. Sourced from sustainable farms.",
    "price": {
      "250g": 12.99,
      "500g": 22.99,
      "1kg": 39.99
    }
  },
  {
    "name": "Organic Cotton T-Shirt",
    "choices": {
      "size_options": ["S", "M", "L", "XL"],
      "color_options": ["Black", "White", "Navy Blue", "Forest Green"]
    },
    "description": "A comfortable and breathable t-shirt made from 100% organic cotton.",
    "price": 25.00
  },
  {
    "name": "Hand-Poured Scented Candle",
    "choices": {
      "scent_options": ["Lavender & Vanilla", "Sandalwood & Amber", "Ocean Breeze"],
      "size_options": ["8oz", "12oz", "16oz"],
      "wick_options": ["Single Wick", "Dual Wick"]
    },
    "description": "A natural soy wax candle, hand-poured with premium essential oils for a clean burn.",
    "price": {
      "8oz": 18.50,
      "12oz": 24.00,
      "16oz": 30.00
    }
  }
]
, "currency":"USD"}