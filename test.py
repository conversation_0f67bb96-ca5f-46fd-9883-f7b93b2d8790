from gemini_live_voice_only_pkg.stream_server import create_gemini_stream
import uvicorn
import os
from dotenv import load_dotenv
load_dotenv()

# 🎛️ Configuration parametersx
API_KEY = os.environ.get("GOOGLE_API_KEY")
VOICE_NAME = "Puck"

# 🎤 Create the FastAPI app with customizable CORS settings
app = create_gemini_stream(
    api_key=API_KEY,
    system_prompt=None,
    voice_name=VOICE_NAME,
    cors_origins=["https://yourdomain.com"],  # specify allowed origins
    cors_allow_credentials=True,
    cors_allow_methods=["*"],
    cors_allow_headers=["*"],
)

if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=7860, reload=True)